# Search Algorithm Performance Report

## Performance Summary

### Average Search Time by File Size

| Algorithm                     |   10K |   50K |   100K |   250K |
|:------------------------------|------:|------:|-------:|-------:|
| Binary Search (Deduplicated)  | 0.001 | 0.001 |  0.002 |  0.003 |
| Hash Set (FrozenSet)          | 0     | 0.001 |  0.001 |  0.001 |
| Hash Set (FrozenSet) (Reread) | 0.013 | 0.014 |  0.013 |  0.028 |
| Linear Search (Optimized)     | 0.615 | 2.845 |  4.717 | 11.459 |
| Memory-Mapped                 | 1.13  | 3.829 |  6.389 | 14.818 |
| Native Grep                   | 1.532 | 1.712 |  2.332 |  3.293 |

### Load Time by File Size

| Algorithm                     |   10K |    50K |   100K |   250K |
|:------------------------------|------:|-------:|-------:|-------:|
| Binary Search (Deduplicated)  | 4.243 | 15.509 | 30.782 | 75.668 |
| Hash Set (FrozenSet)          | 1.997 | 12.788 | 21.116 | 61.483 |
| Hash Set (FrozenSet) (Reread) | 1.989 | 14.022 | 21.43  | 64.484 |
| Linear Search (Optimized)     | 0.001 |  0.001 |  0.001 |  0.002 |
| Memory-Mapped                 | 0.039 |  0.109 |  0.114 |  0.2   |
| Native Grep                   | 0.002 |  0.001 |  0.001 |  0.001 |

## Performance Visualizations

### Load Time Comparison
![Load Time Comparison](load_time_(ms)_chart.png)

### Search Time Comparison
![Search Time Comparison](avg_search_time_(ms)_chart.png)

## Algorithm Characteristics

1. **HashSet (FrozenSet)**: O(1) lookup with memory trade-off
2. **HashSet (FrozenSet) (Reread)**: No initial memory overhead,                but slower search
3. **Linear Search**: Simple implementation,                 high time complexity (O(n))
4. **Binary Search**: O(log n) search with sorting overhead
5. **Memory-Mapped**: Efficient for large files
6. **Native Grep**: System-level optimization
